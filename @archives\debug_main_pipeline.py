#!/usr/bin/env python3
"""
Diagnostic script to identify causes of premature termination in main.py pipeline.

This script performs step-by-step testing to isolate the exact failure point.
"""

import sys
import traceback
import warnings
warnings.filterwarnings('ignore')

def test_imports():
    """Test all imports from main.py to identify import failures."""
    print("🔍 Testing imports...")
    
    import_tests = [
        ("data_handler", ["load_las_files_from_directory", "clean_log_data", "write_results_to_las"]),
        ("config_handler", ["get_input_files", "select_output_directory", "configure_log_selection", 
                           "configure_well_separation", "get_prediction_mode", "configure_hyperparameters", "console_select"]),
        ("ml_core", ["impute_logs", "impute_logs_deep", "MODEL_REGISTRY"]),
        ("reporting", ["generate_qc_report", "create_summary_plots", "generate_final_report",
                      "create_multi_model_comparison_plots", "create_separate_comparison_plots",
                      "create_crossplot_analysis", "create_model_ranking_visualization"])
    ]
    
    failed_imports = []
    
    for module_name, functions in import_tests:
        try:
            print(f"   Testing {module_name}...")
            module = __import__(module_name)
            
            for func_name in functions:
                if not hasattr(module, func_name):
                    failed_imports.append(f"{module_name}.{func_name}")
                    print(f"      ❌ Missing function: {func_name}")
                else:
                    print(f"      ✅ {func_name}")
                    
        except Exception as e:
            failed_imports.append(f"{module_name}: {str(e)}")
            print(f"      ❌ Import failed: {e}")
    
    if failed_imports:
        print(f"\n❌ Import failures detected:")
        for failure in failed_imports:
            print(f"   • {failure}")
        return False
    else:
        print(f"\n✅ All imports successful")
        return True

def test_model_registry():
    """Test MODEL_REGISTRY integrity and MLR model availability."""
    print("\n🔍 Testing MODEL_REGISTRY...")
    
    try:
        from ml_core import MODEL_REGISTRY
        print(f"   ✅ MODEL_REGISTRY imported successfully")
        print(f"   📊 Total models: {len(MODEL_REGISTRY)}")
        
        # Test each model in registry
        problematic_models = []
        
        for model_key, model_config in MODEL_REGISTRY.items():
            try:
                print(f"\n   Testing model: {model_key}")
                
                # Check required fields
                required_fields = ['name', 'model_class', 'hyperparameters']
                for field in required_fields:
                    if field not in model_config:
                        problematic_models.append(f"{model_key}: missing {field}")
                        print(f"      ❌ Missing field: {field}")
                        continue
                
                # Test model class
                model_class = model_config['model_class']
                if model_class is None:
                    print(f"      ⚠️ Model class is None (dependencies not available)")
                    continue
                
                # Test hyperparameters
                hparams = model_config['hyperparameters']
                default_params = {}
                for hp, details in hparams.items():
                    if 'default' in details:
                        default_params[hp] = details['default']
                    else:
                        problematic_models.append(f"{model_key}.{hp}: missing default")
                        print(f"      ❌ Missing default for hyperparameter: {hp}")
                
                # Test model instantiation
                if callable(model_class):
                    try:
                        # Add fixed params if available
                        if 'fixed_params' in model_config:
                            default_params.update(model_config['fixed_params'])
                        
                        model_instance = model_class(**default_params)
                        print(f"      ✅ Model instantiation successful")
                    except Exception as e:
                        problematic_models.append(f"{model_key}: instantiation failed - {str(e)}")
                        print(f"      ❌ Model instantiation failed: {e}")
                else:
                    problematic_models.append(f"{model_key}: model_class not callable")
                    print(f"      ❌ Model class not callable")
                    
            except Exception as e:
                problematic_models.append(f"{model_key}: {str(e)}")
                print(f"      ❌ Error testing model: {e}")
        
        if problematic_models:
            print(f"\n❌ MODEL_REGISTRY issues detected:")
            for issue in problematic_models:
                print(f"   • {issue}")
            return False
        else:
            print(f"\n✅ MODEL_REGISTRY integrity verified")
            return True
            
    except Exception as e:
        print(f"   ❌ MODEL_REGISTRY test failed: {e}")
        traceback.print_exc()
        return False

def test_mlr_integration():
    """Test MLR model integration specifically."""
    print("\n🔍 Testing MLR integration...")
    
    try:
        from ml_core import MODEL_REGISTRY, MLR_AVAILABLE
        
        print(f"   MLR_AVAILABLE: {MLR_AVAILABLE}")
        
        if not MLR_AVAILABLE:
            print(f"   ⚠️ MLR utilities not available - checking import...")
            try:
                from mlr_utils import MLRModelWrapper, create_mlr_model
                print(f"   ✅ MLR utilities can be imported directly")
            except Exception as e:
                print(f"   ❌ MLR utilities import failed: {e}")
                return False
        
        # Test MLR models in registry
        mlr_models = ['linear_regression', 'ridge_regression', 'lasso_regression', 'elastic_net']
        mlr_issues = []
        
        for model_key in mlr_models:
            if model_key in MODEL_REGISTRY:
                model_config = MODEL_REGISTRY[model_key]
                print(f"   Testing {model_key}...")
                
                # Test model class
                model_class = model_config['model_class']
                if model_class is None:
                    mlr_issues.append(f"{model_key}: model_class is None")
                    print(f"      ❌ Model class is None")
                    continue
                
                # Test instantiation with default params
                try:
                    hparams = {hp: details['default'] 
                             for hp, details in model_config['hyperparameters'].items()}
                    if 'fixed_params' in model_config:
                        hparams.update(model_config['fixed_params'])
                    
                    model_instance = model_class(**hparams)
                    print(f"      ✅ Instantiation successful")
                    
                    # Test if it has required methods
                    if hasattr(model_instance, 'fit') and hasattr(model_instance, 'predict'):
                        print(f"      ✅ Required methods available")
                    else:
                        mlr_issues.append(f"{model_key}: missing fit/predict methods")
                        print(f"      ❌ Missing required methods")
                        
                except Exception as e:
                    mlr_issues.append(f"{model_key}: instantiation failed - {str(e)}")
                    print(f"      ❌ Instantiation failed: {e}")
            else:
                mlr_issues.append(f"{model_key}: not found in registry")
                print(f"   ❌ {model_key} not found in registry")
        
        if mlr_issues:
            print(f"\n❌ MLR integration issues:")
            for issue in mlr_issues:
                print(f"   • {issue}")
            return False
        else:
            print(f"\n✅ MLR integration verified")
            return True
            
    except Exception as e:
        print(f"   ❌ MLR integration test failed: {e}")
        traceback.print_exc()
        return False

def test_dependencies():
    """Test critical dependencies."""
    print("\n🔍 Testing critical dependencies...")
    
    critical_packages = [
        'pandas', 'numpy', 'scikit-learn', 'matplotlib',
        'xgboost', 'lightgbm', 'catboost', 'torch'
    ]
    
    missing_packages = []
    
    for package in critical_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError as e:
            missing_packages.append(f"{package}: {str(e)}")
            print(f"   ❌ {package}: {e}")
    
    if missing_packages:
        print(f"\n❌ Missing dependencies:")
        for missing in missing_packages:
            print(f"   • {missing}")
        return False
    else:
        print(f"\n✅ All critical dependencies available")
        return True

def test_file_operations():
    """Test file operations and GUI components."""
    print("\n🔍 Testing file operations...")
    
    try:
        # Test tkinter availability (for GUI dialogs)
        import tkinter as tk
        print(f"   ✅ tkinter available")
        
        # Test file dialog functions
        from config_handler import get_input_files, select_output_directory
        print(f"   ✅ File dialog functions imported")
        
        # Test LAS file handling
        import lasio
        print(f"   ✅ lasio available")
        
        return True
        
    except Exception as e:
        print(f"   ❌ File operations test failed: {e}")
        return False

def test_main_function_structure():
    """Test the main function structure without executing it."""
    print("\n🔍 Testing main function structure...")
    
    try:
        # Import main function
        from main import main
        print(f"   ✅ main function imported")
        
        # Check if it's callable
        if callable(main):
            print(f"   ✅ main function is callable")
        else:
            print(f"   ❌ main function is not callable")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Main function test failed: {e}")
        traceback.print_exc()
        return False

def run_minimal_pipeline_test():
    """Run a minimal version of the pipeline to identify where it fails."""
    print("\n🔍 Running minimal pipeline test...")
    
    try:
        print("   Step 1: Testing imports...")
        from data_handler import load_las_files_from_directory, clean_log_data
        from config_handler import configure_log_selection, configure_well_separation, get_prediction_mode
        from ml_core import impute_logs, MODEL_REGISTRY
        print("   ✅ Core imports successful")
        
        print("   Step 2: Testing MODEL_REGISTRY access...")
        available_models = list(MODEL_REGISTRY.keys())
        print(f"   ✅ Available models: {len(available_models)}")
        
        print("   Step 3: Testing model selection...")
        # Test with a simple model (XGBoost)
        if 'xgboost' in MODEL_REGISTRY:
            model_config = MODEL_REGISTRY['xgboost']
            print(f"   ✅ XGBoost config accessible")
            
            # Test hyperparameter extraction
            hparams = {hp: details['default'] 
                      for hp, details in model_config['hyperparameters'].items()}
            if 'fixed_params' in model_config:
                hparams.update(model_config['fixed_params'])
            print(f"   ✅ Hyperparameters extracted")
            
            # Test model instantiation
            model_class = model_config['model_class']
            if model_class is not None:
                model_instance = model_class(**hparams)
                print(f"   ✅ Model instantiation successful")
            else:
                print(f"   ❌ Model class is None")
                return False
        else:
            print(f"   ❌ XGBoost not found in registry")
            return False
        
        print("   ✅ Minimal pipeline test passed")
        return True
        
    except Exception as e:
        print(f"   ❌ Minimal pipeline test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run comprehensive diagnostic tests."""
    print("🚀 ML Log Prediction Pipeline Diagnostic")
    print("=" * 60)
    
    tests = [
        ("Import Tests", test_imports),
        ("Dependency Tests", test_dependencies),
        ("MODEL_REGISTRY Tests", test_model_registry),
        ("MLR Integration Tests", test_mlr_integration),
        ("File Operations Tests", test_file_operations),
        ("Main Function Tests", test_main_function_structure),
        ("Minimal Pipeline Tests", run_minimal_pipeline_test)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    passed_tests = []
    failed_tests = []
    
    for test_name, result in results.items():
        if result:
            passed_tests.append(test_name)
            print(f"✅ {test_name}")
        else:
            failed_tests.append(test_name)
            print(f"❌ {test_name}")
    
    print(f"\n📈 Results: {len(passed_tests)}/{len(tests)} tests passed")
    
    if failed_tests:
        print(f"\n🔧 RECOMMENDED ACTIONS:")
        
        if "Import Tests" in failed_tests:
            print("   1. Check for missing or corrupted module files")
            print("   2. Verify Python path and working directory")
            
        if "Dependency Tests" in failed_tests:
            print("   3. Install missing packages: pip install -r requirements.txt")
            print("   4. Check for package version conflicts")
            
        if "MODEL_REGISTRY Tests" in failed_tests:
            print("   5. Check ml_core.py for MODEL_REGISTRY corruption")
            print("   6. Verify model class definitions")
            
        if "MLR Integration Tests" in failed_tests:
            print("   7. Check mlr_utils.py for MLR implementation issues")
            print("   8. Verify MLR model integration in MODEL_REGISTRY")
            
        if "File Operations Tests" in failed_tests:
            print("   9. Check tkinter installation for GUI dialogs")
            print("   10. Verify lasio package for LAS file handling")
            
        if "Main Function Tests" in failed_tests:
            print("   11. Check main.py for syntax errors")
            print("   12. Verify main function definition")
            
        if "Minimal Pipeline Tests" in failed_tests:
            print("   13. Run with verbose logging to identify exact failure point")
            print("   14. Test with minimal data and single model")
    
    else:
        print(f"\n🎉 All diagnostic tests passed!")
        print(f"   The pipeline should work correctly.")
        print(f"   If you're still experiencing issues, try:")
        print(f"   • Running with verbose output")
        print(f"   • Testing with minimal data")
        print(f"   • Checking system resources (memory, disk space)")
    
    return 0 if not failed_tests else 1

if __name__ == "__main__":
    sys.exit(main())
