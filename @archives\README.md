# Archives Directory

This directory contains archived files that are not part of the main ML log prediction pipeline but are preserved for historical reference.

## Contents

### Python Scripts (5 files)
- **debug_main_pipeline.py** - Diagnostic script for troubleshooting the main pipeline
- **data_leakage_detector.py** - Utility for detecting data leakage in ML models
- **enhanced_preprocessing.py** - Experimental preprocessing functions
- **mlr_utils.py** - Utility functions for MLR (Multiple Linear Regression) operations
- **setup_gpu_environment.py** - GPU environment configuration utility

### Example/Tutorial Content
- **example/** - PyPOTS tutorial files and examples
  - PyPOTS_Quick_Start.ipynb - Jupyter notebook tutorial
  - Pypots_quick_start_tutor.py - Python tutorial script
- **tutorial_results/** - Results from PyPOTS tutorial runs
  - classification/ - Classification example results
  - forecasting/ - Forecasting example results  
  - imputation/ - Imputation example results

### Artifacts
- **visualization_fix_comparison.png** - Test visualization comparison image

## Archive Date
**Moved on**: 2025-07-24

## Reason for Archival
These files were moved to clean up the main directory and focus on the core ML log prediction pipeline. All archived files remain functional and can be used independently if needed.

## Core Pipeline Files (Remained in Root)
- main.py - Main entry point
- config_handler.py - Configuration management
- data_handler.py - Data processing
- ml_core.py - Core ML functionality
- reporting.py - Reporting and visualization
