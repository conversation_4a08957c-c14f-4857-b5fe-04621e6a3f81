#!/usr/bin/env python3
"""
GPU Environment Setup for ML Log Prediction
Configures optimal environment variables and settings for GPU memory management.
"""

import os
import sys
import torch
import warnings
from typing import Dict, Any

def configure_pytorch_environment():
    """Configure PyTorch environment variables for optimal GPU memory usage."""
    
    print("🔧 Configuring PyTorch environment for optimal GPU memory usage...")
    
    # Set CUDA memory allocator configuration
    # expandable_segments:True allows PyTorch to request additional memory from CUDA
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    print("   ✅ Set PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True")
    
    # Additional memory optimization settings
    memory_configs = {
        # Reduce memory fragmentation
        'PYTORCH_CUDA_ALLOC_CONF': 'expandable_segments:True,max_split_size_mb:128',
        
        # Enable memory pool for better allocation
        'CUDA_LAUNCH_BLOCKING': '0',  # Async execution for better performance
        
        # Optimize CUDA context
        'CUDA_CACHE_DISABLE': '0',  # Enable CUDA cache
        
        # Set memory growth strategy
        'TF_FORCE_GPU_ALLOW_GROWTH': 'true',  # For TensorFlow compatibility
    }
    
    for key, value in memory_configs.items():
        if key not in os.environ:
            os.environ[key] = value
            print(f"   ✅ Set {key}={value}")
        else:
            print(f"   ℹ️ {key} already set to: {os.environ[key]}")

def configure_cuda_settings():
    """Configure CUDA-specific settings for memory optimization."""
    
    if not torch.cuda.is_available():
        print("⚠️ CUDA not available, skipping CUDA-specific configuration")
        return
    
    print("🚀 Configuring CUDA settings...")
    
    try:
        # Set memory fraction to use 90% of GPU memory
        torch.cuda.set_per_process_memory_fraction(0.9)
        print("   ✅ Set GPU memory fraction to 90%")
        
        # Enable memory pool
        torch.cuda.empty_cache()
        print("   ✅ Cleared CUDA cache")
        
        # Get GPU information
        device_count = torch.cuda.device_count()
        print(f"   📊 Found {device_count} CUDA device(s)")
        
        for i in range(device_count):
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / (1024**3)
            print(f"   🚀 GPU {i}: {props.name} ({memory_gb:.1f} GB)")
            
    except Exception as e:
        print(f"   ⚠️ CUDA configuration warning: {e}")

def configure_system_settings():
    """Configure system-level settings for optimal performance."""
    
    print("💻 Configuring system settings...")
    
    # Python-specific optimizations
    sys.dont_write_bytecode = True  # Reduce I/O overhead
    
    # Warning filters for cleaner output
    warnings.filterwarnings('ignore', category=UserWarning, module='torch')
    warnings.filterwarnings('ignore', category=FutureWarning, module='torch')
    
    print("   ✅ Configured Python optimizations")
    print("   ✅ Filtered non-critical warnings")

def print_environment_summary():
    """Print a summary of the configured environment."""
    
    print("\n" + "="*60)
    print("🌟 GPU ENVIRONMENT CONFIGURATION SUMMARY")
    print("="*60)
    
    # PyTorch information
    print(f"🔥 PyTorch Version: {torch.__version__}")
    print(f"🐍 Python Version: {sys.version.split()[0]}")
    
    # CUDA information
    if torch.cuda.is_available():
        print(f"🚀 CUDA Available: ✅")
        print(f"🚀 CUDA Version: {torch.version.cuda}")
        print(f"🚀 cuDNN Version: {torch.backends.cudnn.version()}")
        print(f"🚀 GPU Count: {torch.cuda.device_count()}")
        
        # Current GPU memory status
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            allocated = torch.cuda.memory_allocated(i) / (1024**3)
            total = props.total_memory / (1024**3)
            print(f"🚀 GPU {i} Memory: {allocated:.1f}/{total:.1f} GB")
    else:
        print(f"🚀 CUDA Available: ❌")
    
    # Environment variables
    print(f"\n📋 Key Environment Variables:")
    env_vars = [
        'PYTORCH_CUDA_ALLOC_CONF',
        'CUDA_LAUNCH_BLOCKING',
        'CUDA_CACHE_DISABLE',
        'TF_FORCE_GPU_ALLOW_GROWTH'
    ]
    
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"   • {var}: {value}")
    
    print("="*60)

def setup_memory_monitoring():
    """Setup memory monitoring if utilities are available."""
    
    try:
        from utils.memory_optimization import get_memory_optimizer
        from utils.performance_monitor import get_performance_monitor
        
        print("🔍 Initializing memory monitoring...")
        
        # Initialize memory optimizer
        memory_optimizer = get_memory_optimizer()
        memory_optimizer.print_memory_status()
        
        # Initialize performance monitor
        performance_monitor = get_performance_monitor()
        
        print("   ✅ Memory monitoring initialized")
        
        return memory_optimizer, performance_monitor
        
    except ImportError:
        print("   ⚠️ Memory monitoring utilities not available")
        return None, None

def main():
    """Main setup function."""
    
    print("🚀 Starting GPU Environment Setup for ML Log Prediction")
    print("="*60)
    
    # Configure environment
    configure_pytorch_environment()
    configure_cuda_settings()
    configure_system_settings()
    
    # Setup monitoring
    memory_optimizer, performance_monitor = setup_memory_monitoring()
    
    # Print summary
    print_environment_summary()
    
    print("\n✅ GPU environment setup completed!")
    print("💡 Your system is now optimized for large dataset processing")
    
    if torch.cuda.is_available():
        print("🚀 GPU acceleration is ready for SAITS model training and prediction")
    else:
        print("💻 CPU processing is configured with optimizations")
    
    return {
        'memory_optimizer': memory_optimizer,
        'performance_monitor': performance_monitor,
        'cuda_available': torch.cuda.is_available(),
        'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
    }

if __name__ == '__main__':
    setup_info = main()
    
    # Optional: Start performance monitoring
    if setup_info['performance_monitor']:
        choice = input("\n🔍 Start performance monitoring? (y/n): ").strip().lower()
        if choice == 'y':
            setup_info['performance_monitor'].start_monitoring()
            print("📊 Performance monitoring started")
            print("💡 Use Ctrl+C to stop monitoring and view report")
            
            try:
                input("Press Enter to stop monitoring...")
            except KeyboardInterrupt:
                pass
            finally:
                setup_info['performance_monitor'].stop_monitoring()
                setup_info['performance_monitor'].print_performance_report()
